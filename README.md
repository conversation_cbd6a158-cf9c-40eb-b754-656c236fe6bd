# 通用数据同步策略系统

这是一个基于Spring Boot的通用数据同步策略系统，支持多种数据源之间的数据对比和迁移。

## 功能特性

### 支持的数据源
- **MySQL** - 关系型数据库
- **Redis** - 内存数据库
- **Elasticsearch** - 搜索引擎

### 支持的操作类型
- **COMPARE** - 数据对比：比较两个数据源的数据差异
- **MIGRATE** - 数据迁移：将数据从源端迁移到目标端
- **SYNC** - 数据同步：先对比再迁移差异数据

### 核心功能
- 🔄 **通用适配器模式** - 统一不同数据源的访问接口
- 📊 **灵活的对比策略** - 支持字段级别的数据对比
- 🚀 **高效的迁移策略** - 支持批量数据迁移
- ⚙️ **丰富的配置选项** - 支持字段映射、忽略字段等
- 📈 **详细的执行报告** - 提供完整的同步统计和错误信息

## 快速开始

### 1. 添加依赖

项目已包含必要的依赖，包括：
- Spring Boot Starter Web
- Spring Boot Starter Data JPA
- Spring Boot Starter Data Redis
- Spring Boot Starter Data Elasticsearch
- MySQL Connector
- Jackson
- Lombok

### 2. 配置数据源

在 `application.yml` 中配置数据源连接信息：

```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
  
  elasticsearch:
    uris: http://localhost:9200
    username: elastic
    password: your_es_password
```

### 3. 使用示例

#### MySQL到MySQL数据对比

```java
SyncConfig syncConfig = SyncConfig.builder()
    .taskName("MySQL用户表对比")
    .syncType(SyncConfig.SyncType.COMPARE)
    .sourceConfig(SyncConfig.createMySQLConfig(
        "*************************************",
        "root", "password", "source_db", "users"
    ))
    .targetConfig(SyncConfig.createMySQLConfig(
        "*************************************", 
        "root", "password", "target_db", "users"
    ))
    .batchSize(1000)
    .ignoreFields(List.of("create_time", "update_time"))
    .build();

DataSyncEngine.SyncResult result = dataSyncEngine.executeSync(syncConfig);
```

#### MySQL到Redis数据迁移

```java
Map<String, String> fieldMapping = new HashMap<>();
fieldMapping.put("user_id", "id");
fieldMapping.put("user_name", "name");

SyncConfig syncConfig = SyncConfig.builder()
    .taskName("MySQL到Redis迁移")
    .syncType(SyncConfig.SyncType.MIGRATE)
    .sourceConfig(SyncConfig.createMySQLConfig(
        "**********************************",
        "root", "password", "app_db", "users"
    ))
    .targetConfig(SyncConfig.createRedisConfig(
        "redis://localhost:6379", "password", "user"
    ))
    .fieldMapping(fieldMapping)
    .skipExisting(true)
    .build();

DataSyncEngine.SyncResult result = dataSyncEngine.executeSync(syncConfig);
```

#### MySQL到Elasticsearch数据同步

```java
SyncConfig syncConfig = SyncConfig.builder()
    .taskName("MySQL到ES同步")
    .syncType(SyncConfig.SyncType.SYNC)
    .sourceConfig(SyncConfig.createMySQLConfig(
        "*************************************",
        "root", "password", "ecommerce", "products"
    ))
    .targetConfig(SyncConfig.createElasticsearchConfig(
        "http://localhost:9200", "elastic", "password", "products"
    ))
    .batchSize(1000)
    .build();

DataSyncEngine.SyncResult result = dataSyncEngine.executeSync(syncConfig);
```

## REST API接口

### 执行同步任务
```http
POST /api/data-sync/execute
Content-Type: application/json

{
  "taskName": "数据同步任务",
  "syncType": "SYNC",
  "sourceConfig": {
    "type": "MySQL",
    "url": "*************************************",
    "username": "root",
    "password": "password",
    "database": "source_db",
    "tableName": "users"
  },
  "targetConfig": {
    "type": "Redis",
    "url": "redis://localhost:6379",
    "password": "redis_password",
    "tableName": "user"
  },
  "batchSize": 1000,
  "skipExisting": true
}
```

### MySQL到MySQL对比
```http
POST /api/data-sync/compare/mysql-to-mysql
Content-Type: application/json

{
  "sourceUrl": "*******************************",
  "sourceUsername": "root",
  "sourcePassword": "password",
  "sourceDatabase": "db1",
  "sourceTable": "users",
  "targetUrl": "*******************************",
  "targetUsername": "root", 
  "targetPassword": "password",
  "targetDatabase": "db2",
  "targetTable": "users",
  "batchSize": 1000,
  "ignoreFields": ["create_time", "update_time"]
}
```

### 获取支持的数据源类型
```http
GET /api/data-sync/supported-datasources
```

### 获取配置模板
```http
GET /api/data-sync/config-template
```

## 配置选项

### 基础配置
- `taskName`: 任务名称
- `syncType`: 同步类型（COMPARE/MIGRATE/SYNC）
- `batchSize`: 批处理大小，默认1000
- `skipExisting`: 是否跳过已存在记录，默认false
- `continueOnError`: 遇到错误是否继续，默认true

### 高级配置
- `fieldMapping`: 字段映射配置
- `ignoreFields`: 忽略的字段列表
- `deepCompare`: 是否深度对比，默认true
- `customConfig`: 自定义配置参数

## 架构设计

### 核心组件

1. **DataSourceAdapter** - 数据源适配器接口
   - MySQLDataSourceAdapter
   - RedisDataSourceAdapter  
   - ElasticsearchDataSourceAdapter

2. **DataStrategy** - 数据策略接口
   - DataCompareStrategy - 数据对比策略
   - DataMigrationStrategy - 数据迁移策略

3. **DataSyncEngine** - 数据同步引擎
   - 协调整个同步过程
   - 管理数据源适配器
   - 执行同步策略

4. **SyncConfig** - 同步配置
   - 封装同步任务的所有配置参数

### 设计模式

- **适配器模式** - 统一不同数据源的访问接口
- **策略模式** - 支持不同的对比和迁移策略
- **建造者模式** - 简化配置对象的创建
- **模板方法模式** - 定义同步流程的骨架

## 扩展指南

### 添加新的数据源

1. 实现 `DataSourceAdapter` 接口
2. 在 `DataSyncEngine` 中添加适配器创建逻辑
3. 更新配置模板和文档

### 自定义对比策略

1. 实现 `DataCompareStrategy` 接口
2. 注册为Spring Bean
3. 在配置中指定策略名称

### 自定义迁移策略

1. 实现 `DataMigrationStrategy` 接口
2. 注册为Spring Bean
3. 在配置中指定策略名称

## 注意事项

1. **性能优化**
   - 合理设置批处理大小
   - 对大表使用分页查询
   - 考虑使用连接池

2. **数据一致性**
   - 在事务中执行关键操作
   - 处理并发访问问题
   - 实现重试机制

3. **错误处理**
   - 记录详细的错误日志
   - 提供错误恢复机制
   - 监控同步任务状态

4. **安全性**
   - 加密存储数据库密码
   - 使用安全的连接方式
   - 限制访问权限

## 许可证

MIT License
