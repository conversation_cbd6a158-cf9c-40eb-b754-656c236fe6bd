package com.jiao.data.sync;

import com.jiao.data.config.SyncConfig;
import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.datasource.impl.ElasticsearchDataSourceAdapter;
import com.jiao.data.datasource.impl.MySQLDataSourceAdapter;
import com.jiao.data.datasource.impl.RedisDataSourceAdapter;
import com.jiao.data.strategy.DataCompareStrategy;
import com.jiao.data.strategy.DataMigrationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

/**
 * 数据同步引擎
 * 协调整个数据同步过程
 */
@Slf4j
@Component
public class DataSyncEngine {
    
    @Autowired
    private DataCompareStrategy compareStrategy;
    
    @Autowired
    private DataMigrationStrategy migrationStrategy;
    
    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired(required = false)
    private ElasticsearchTemplate elasticsearchTemplate;
    
    /**
     * 执行数据同步任务
     */
    public SyncResult executeSync(SyncConfig syncConfig) {
        log.info("开始执行数据同步任务: {}", syncConfig.getTaskName());
        
        // 验证配置
        if (!syncConfig.isValid()) {
            return SyncResult.failure("同步配置无效");
        }
        
        SyncResult result = new SyncResult();
        result.setTaskId(syncConfig.getTaskId());
        result.setStartTime(System.currentTimeMillis());
        
        try {
            // 创建数据源适配器
            DataSourceAdapter sourceAdapter = createDataSourceAdapter(syncConfig.getSourceConfig());
            DataSourceAdapter targetAdapter = createDataSourceAdapter(syncConfig.getTargetConfig());
            
            if (sourceAdapter == null || targetAdapter == null) {
                return SyncResult.failure("无法创建数据源适配器");
            }
            
            // 测试连接
            if (!sourceAdapter.testConnection()) {
                return SyncResult.failure("源数据源连接失败");
            }
            
            if (!targetAdapter.testConnection()) {
                return SyncResult.failure("目标数据源连接失败");
            }
            
            // 根据同步类型执行相应操作
            switch (syncConfig.getSyncType()) {
                case COMPARE:
                    result = executeCompare(sourceAdapter, targetAdapter, syncConfig);
                    break;
                case MIGRATE:
                    result = executeMigrate(sourceAdapter, targetAdapter, syncConfig);
                    break;
                case SYNC:
                    result = executeSync(sourceAdapter, targetAdapter, syncConfig);
                    break;
                default:
                    result = SyncResult.failure("不支持的同步类型: " + syncConfig.getSyncType());
            }
            
        } catch (Exception e) {
            log.error("执行数据同步任务失败", e);
            result = SyncResult.failure("同步过程中发生异常: " + e.getMessage());
        } finally {
            result.setEndTime(System.currentTimeMillis());
        }
        
        log.info("数据同步任务完成: {}, 耗时: {}ms", syncConfig.getTaskName(), result.getDuration());
        return result;
    }
    
    /**
     * 执行数据对比
     */
    private SyncResult executeCompare(DataSourceAdapter sourceAdapter, 
                                    DataSourceAdapter targetAdapter, 
                                    SyncConfig syncConfig) {
        
        log.info("执行数据对比");
        
        List<DataCompareStrategy.CompareResult> compareResults = compareStrategy.compare(
            sourceAdapter, 
            targetAdapter,
            syncConfig.getSourceConfig().getTableName(),
            syncConfig.getTargetConfig().getTableName(),
            syncConfig.getMergedConfig()
        );
        
        String report = compareStrategy.generateCompareReport(compareResults);
        
        return SyncResult.success("数据对比完成")
                .setCompareResults(compareResults)
                .setReport(report);
    }
    
    /**
     * 执行数据迁移
     */
    private SyncResult executeMigrate(DataSourceAdapter sourceAdapter,
                                    DataSourceAdapter targetAdapter,
                                    SyncConfig syncConfig) {
        
        log.info("执行数据迁移");
        
        DataMigrationStrategy.MigrationStatistics statistics = migrationStrategy.migrate(
            sourceAdapter,
            targetAdapter,
            syncConfig.getSourceConfig().getTableName(),
            syncConfig.getTargetConfig().getTableName(),
            syncConfig.getMergedConfig()
        );
        
        String report = migrationStrategy.generateMigrationReport(statistics, null);
        
        return SyncResult.success("数据迁移完成")
                .setMigrationStatistics(statistics)
                .setReport(report);
    }
    
    /**
     * 执行完整同步（先对比，再迁移差异数据）
     */
    private SyncResult executeSync(DataSourceAdapter sourceAdapter,
                                 DataSourceAdapter targetAdapter,
                                 SyncConfig syncConfig) {
        
        log.info("执行完整数据同步");
        
        // 先执行对比
        SyncResult compareResult = executeCompare(sourceAdapter, targetAdapter, syncConfig);
        if (!compareResult.isSuccess()) {
            return compareResult;
        }
        
        // 分析对比结果，确定需要迁移的数据
        List<DataCompareStrategy.CompareResult> compareResults = compareResult.getCompareResults();
        long needMigrationCount = compareResults.stream()
            .filter(result -> result.getType() == DataCompareStrategy.CompareResultType.SOURCE_ONLY ||
                            result.getType() == DataCompareStrategy.CompareResultType.DIFFERENT)
            .count();
        
        if (needMigrationCount == 0) {
            return SyncResult.success("数据已同步，无需迁移")
                    .setCompareResults(compareResults)
                    .setReport(compareResult.getReport());
        }
        
        // 执行迁移
        SyncResult migrateResult = executeMigrate(sourceAdapter, targetAdapter, syncConfig);
        
        // 合并结果
        StringBuilder combinedReport = new StringBuilder();
        combinedReport.append("=== 完整同步报告 ===\n\n");
        combinedReport.append("对比阶段:\n");
        combinedReport.append(compareResult.getReport());
        combinedReport.append("\n\n迁移阶段:\n");
        combinedReport.append(migrateResult.getReport());
        
        return SyncResult.success("完整数据同步完成")
                .setCompareResults(compareResults)
                .setMigrationStatistics(migrateResult.getMigrationStatistics())
                .setReport(combinedReport.toString());
    }
    
    /**
     * 创建数据源适配器
     */
    private DataSourceAdapter createDataSourceAdapter(SyncConfig.DataSourceConfig config) {
        try {
            switch (config.getType().toLowerCase()) {
                case "mysql":
                    return createMySQLAdapter(config);
                case "redis":
                    return createRedisAdapter(config);
                case "elasticsearch":
                    return createElasticsearchAdapter(config);
                default:
                    log.error("不支持的数据源类型: {}", config.getType());
                    return null;
            }
        } catch (Exception e) {
            log.error("创建数据源适配器失败: {}", config.getType(), e);
            return null;
        }
    }
    
    private DataSourceAdapter createMySQLAdapter(SyncConfig.DataSourceConfig config) {
        DataSource dataSource = DataSourceBuilder.create()
                .url(config.getUrl())
                .username(config.getUsername())
                .password(config.getPassword())
                .driverClassName("com.mysql.cj.jdbc.Driver")
                .build();
        
        return new MySQLDataSourceAdapter(dataSource, config.getDatabase());
    }
    
    private DataSourceAdapter createRedisAdapter(SyncConfig.DataSourceConfig config) {
        if (redisTemplate == null) {
            throw new IllegalStateException("RedisTemplate未配置");
        }
        
        // 这里简化处理，实际使用时可能需要根据config创建新的RedisTemplate
        String keyPrefix = (String) config.getProperties().getOrDefault("keyPrefix", "");
        return new RedisDataSourceAdapter(redisTemplate, keyPrefix);
    }
    
    private DataSourceAdapter createElasticsearchAdapter(SyncConfig.DataSourceConfig config) {
        if (elasticsearchTemplate == null) {
            throw new IllegalStateException("ElasticsearchTemplate未配置");
        }
        
        // 这里简化处理，实际使用时可能需要根据config创建新的ElasticsearchTemplate
        return new ElasticsearchDataSourceAdapter(elasticsearchTemplate);
    }
    
    /**
     * 同步结果类
     */
    public static class SyncResult {
        private String taskId;
        private boolean success;
        private String message;
        private long startTime;
        private long endTime;
        private List<DataCompareStrategy.CompareResult> compareResults;
        private DataMigrationStrategy.MigrationStatistics migrationStatistics;
        private String report;
        
        public static SyncResult success(String message) {
            SyncResult result = new SyncResult();
            result.success = true;
            result.message = message;
            return result;
        }
        
        public static SyncResult failure(String message) {
            SyncResult result = new SyncResult();
            result.success = false;
            result.message = message;
            return result;
        }
        
        // Getters and Setters
        public String getTaskId() { return taskId; }
        public SyncResult setTaskId(String taskId) { this.taskId = taskId; return this; }
        
        public boolean isSuccess() { return success; }
        public SyncResult setSuccess(boolean success) { this.success = success; return this; }
        
        public String getMessage() { return message; }
        public SyncResult setMessage(String message) { this.message = message; return this; }
        
        public long getStartTime() { return startTime; }
        public SyncResult setStartTime(long startTime) { this.startTime = startTime; return this; }
        
        public long getEndTime() { return endTime; }
        public SyncResult setEndTime(long endTime) { this.endTime = endTime; return this; }
        
        public List<DataCompareStrategy.CompareResult> getCompareResults() { return compareResults; }
        public SyncResult setCompareResults(List<DataCompareStrategy.CompareResult> compareResults) { 
            this.compareResults = compareResults; return this; 
        }
        
        public DataMigrationStrategy.MigrationStatistics getMigrationStatistics() { return migrationStatistics; }
        public SyncResult setMigrationStatistics(DataMigrationStrategy.MigrationStatistics migrationStatistics) { 
            this.migrationStatistics = migrationStatistics; return this; 
        }
        
        public String getReport() { return report; }
        public SyncResult setReport(String report) { this.report = report; return this; }
        
        public long getDuration() { return endTime - startTime; }
    }
}
