package com.jiao.data.strategy.impl;

import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.model.DataRecord;
import com.jiao.data.strategy.DataMigrationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用数据迁移策略实现
 */
@Slf4j
@Component
public class GenericDataMigrationStrategy implements DataMigrationStrategy {

    @Override
    public String getStrategyName() {
        return "GenericDataMigrationStrategy";
    }

    @Override
    public boolean supports(String sourceType, String targetType) {
        // 支持所有数据源类型的迁移
        return true;
    }

    @Override
    public MigrationStatistics migrate(DataSourceAdapter sourceAdapter,
                                       DataSourceAdapter targetAdapter,
                                       String sourceTable,
                                       String targetTable,
                                       Map<String, Object> migrationConfig) {

        log.info("开始数据迁移: {} -> {}", sourceAdapter.getDataSourceType(), targetAdapter.getDataSourceType());

        MigrationStatistics statistics = new MigrationStatistics();
        statistics.setStartTime(System.currentTimeMillis());

        List<MigrationResult> allResults = new ArrayList<>();

        try {
            // 获取配置参数
            int batchSize = (Integer) migrationConfig.getOrDefault("batchSize", 1000);
            boolean skipExisting = (Boolean) migrationConfig.getOrDefault("skipExisting", false);
            boolean continueOnError = (Boolean) migrationConfig.getOrDefault("continueOnError", true);

            // 获取源数据总数
            long totalRecords = sourceAdapter.count(sourceTable);
            statistics.setTotalRecords(totalRecords);

            log.info("准备迁移 {} 条记录，批次大小: {}", totalRecords, batchSize);

            // 分批处理数据
            for (int offset = 0; offset < totalRecords; offset += batchSize) {
                List<DataRecord> sourceRecords = sourceAdapter.queryData(sourceTable, offset, batchSize);

                if (sourceRecords.isEmpty()) {
                    break;
                }

                log.info("处理批次: {}-{}/{}", offset + 1, Math.min(offset + batchSize, totalRecords), totalRecords);

                // 转换记录
                List<DataRecord> transformedRecords = sourceRecords.stream()
                        .map(record -> transformRecord(record, targetAdapter, migrationConfig))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 批量迁移
                List<MigrationResult> batchResults = batchMigrateRecords(
                        transformedRecords, targetAdapter, targetTable, migrationConfig);

                allResults.addAll(batchResults);

                // 更新统计信息
                updateStatistics(statistics, batchResults);

                // 如果不继续处理错误且有失败记录，则停止迁移
                if (!continueOnError && batchResults.stream()
                        .anyMatch(result -> result.getType() == MigrationResultType.FAILED)) {
                    log.warn("遇到错误，停止迁移");
                    break;
                }
            }

        } catch (Exception e) {
            log.error("数据迁移过程中发生错误", e);
        } finally {
            statistics.setEndTime(System.currentTimeMillis());
        }

        log.info("数据迁移完成，耗时: {}ms, 成功: {}, 失败: {}, 跳过: {}",
                statistics.getDuration(), statistics.getSuccessCount(),
                statistics.getFailedCount(), statistics.getSkippedCount());

        return statistics;
    }

    private void updateStatistics(MigrationStatistics statistics, List<MigrationResult> results) {
        for (MigrationResult result : results) {
            switch (result.getType()) {
                case SUCCESS:
                    statistics.setSuccessCount(statistics.getSuccessCount() + 1);
                    break;
                case FAILED:
                    statistics.setFailedCount(statistics.getFailedCount() + 1);
                    break;
                case SKIPPED:
                    statistics.setSkippedCount(statistics.getSkippedCount() + 1);
                    break;
                case PARTIAL_SUCCESS:
                    statistics.setSuccessCount(statistics.getSuccessCount() + 1);
                    break;
            }
        }
    }

    @Override
    public MigrationResult migrateRecord(DataRecord sourceRecord,
                                         DataSourceAdapter targetAdapter,
                                         String targetTable,
                                         Map<String, Object> migrationConfig) {

        try {
            // 转换记录
            DataRecord transformedRecord = transformRecord(sourceRecord, targetAdapter, migrationConfig);
            if (transformedRecord == null) {
                return new MigrationResult(sourceRecord.getId(), MigrationResultType.SKIPPED,
                        "记录转换失败", sourceRecord, null, null);
            }

            // 检查是否跳过已存在的记录
            boolean skipExisting = (Boolean) migrationConfig.getOrDefault("skipExisting", false);
            if (skipExisting) {
                DataRecord existingRecord = targetAdapter.queryById(targetTable, transformedRecord.getId());
                if (existingRecord != null) {
                    return new MigrationResult(sourceRecord.getId(), MigrationResultType.SKIPPED,
                            "记录已存在", sourceRecord, existingRecord, null);
                }
            }

            // 插入记录
            boolean success = targetAdapter.insert(targetTable, transformedRecord);
            if (success) {
                return new MigrationResult(sourceRecord.getId(), MigrationResultType.SUCCESS,
                        "迁移成功", sourceRecord, transformedRecord, null);
            } else {
                return new MigrationResult(sourceRecord.getId(), MigrationResultType.FAILED,
                        "插入失败", sourceRecord, transformedRecord, null);
            }

        } catch (Exception e) {
            log.error("迁移记录失败: {}", sourceRecord.getId(), e);
            return new MigrationResult(sourceRecord.getId(), MigrationResultType.FAILED,
                    "迁移异常: " + e.getMessage(), sourceRecord, null, e);
        }
    }

    @Override
    public List<MigrationResult> batchMigrateRecords(List<DataRecord> sourceRecords,
                                                     DataSourceAdapter targetAdapter,
                                                     String targetTable,
                                                     Map<String, Object> migrationConfig) {

        List<MigrationResult> results = new ArrayList<>();

        try {
            // 尝试批量插入
            boolean batchSuccess = targetAdapter.batchInsert(targetTable, sourceRecords);

            if (batchSuccess) {
                // 批量插入成功，为所有记录创建成功结果
                for (DataRecord record : sourceRecords) {
                    results.add(new MigrationResult(record.getId(), MigrationResultType.SUCCESS,
                            "批量迁移成功", record, record, null));
                }
            } else {
                // 批量插入失败，逐条处理
                log.warn("批量插入失败，改为逐条处理");
                for (DataRecord record : sourceRecords) {
                    MigrationResult result = migrateRecord(record, targetAdapter, targetTable, migrationConfig);
                    results.add(result);
                }
            }

        } catch (Exception e) {
            log.error("批量迁移失败，改为逐条处理", e);
            // 批量操作异常，逐条处理
            for (DataRecord record : sourceRecords) {
                try {
                    MigrationResult result = migrateRecord(record, targetAdapter, targetTable, migrationConfig);
                    results.add(result);
                } catch (Exception ex) {
                    results.add(new MigrationResult(record.getId(), MigrationResultType.FAILED,
                            "迁移异常: " + ex.getMessage(), record, null, ex));
                }
            }
        }

        return results;
    }

    @Override
    public DataRecord transformRecord(DataRecord sourceRecord,
                                      DataSourceAdapter targetAdapter,
                                      Map<String, Object> migrationConfig) {

        if (sourceRecord == null) {
            return null;
        }

        try {
            // 获取字段映射配置
            Map<String, String> fieldMapping = (Map<String, String>)
                    migrationConfig.getOrDefault("fieldMapping", new HashMap<>());

            // 获取忽略字段配置
            List<String> ignoreFields = (List<String>)
                    migrationConfig.getOrDefault("ignoreFields", new ArrayList<>());

            // 创建新的数据记录
            Map<String, Object> transformedData = new HashMap<>();

            if (sourceRecord.getData() != null) {
                for (Map.Entry<String, Object> entry : sourceRecord.getData().entrySet()) {
                    String sourceField = entry.getKey();
                    Object value = entry.getValue();

                    // 跳过忽略的字段
                    if (ignoreFields.contains(sourceField)) {
                        continue;
                    }

                    // 应用字段映射
                    String targetField = fieldMapping.getOrDefault(sourceField, sourceField);

                    // 转换数据类型（根据目标数据源类型）
                    Object transformedValue = transformValue(value, targetAdapter.getDataSourceType());

                    transformedData.put(targetField, transformedValue);
                }
            }

            return DataRecord.builder()
                    .id(sourceRecord.getId())
                    .data(transformedData)
                    .sourceType(targetAdapter.getDataSourceType())
                    .tableName(sourceRecord.getTableName())
                    .createTime(LocalDateTime.now())
                    .updateTime(LocalDateTime.now())
                    .build();

        } catch (Exception e) {
            log.error("转换记录失败: {}", sourceRecord.getId(), e);
            return null;
        }
    }

    private Object transformValue(Object value, String targetDataSourceType) {
        if (value == null) {
            return null;
        }

        // 根据目标数据源类型进行特殊转换
        switch (targetDataSourceType.toLowerCase()) {
            case "redis":
                // Redis通常存储字符串，复杂对象需要序列化
                if (value instanceof Map || value instanceof List) {
                    return value; // RedisTemplate会自动处理序列化
                }
                return value.toString();

            case "elasticsearch":
                // Elasticsearch支持复杂对象，但需要注意日期格式
                if (value instanceof Date) {
                    return ((Date) value).toInstant().toString();
                }
                return value;

            case "mysql":
            default:
                // MySQL等关系型数据库的标准转换
                return value;
        }
    }

    @Override
    public String generateMigrationReport(MigrationStatistics statistics, List<MigrationResult> results) {
        StringBuilder report = new StringBuilder();
        report.append("=== 数据迁移报告 ===\n");
        report.append("生成时间: ").append(new Date()).append("\n\n");

        // 统计信息
        report.append("统计信息:\n");
        report.append("- 总记录数: ").append(statistics.getTotalRecords()).append("\n");
        report.append("- 成功数量: ").append(statistics.getSuccessCount()).append("\n");
        report.append("- 失败数量: ").append(statistics.getFailedCount()).append("\n");
        report.append("- 跳过数量: ").append(statistics.getSkippedCount()).append("\n");
        report.append("- 耗时: ").append(statistics.getDuration()).append("ms\n");
        report.append("- 成功率: ").append(String.format("%.2f%%",
                (double) statistics.getSuccessCount() / statistics.getTotalRecords() * 100)).append("\n\n");

        // 失败记录详情（前50条）
        if (results != null) {
            List<MigrationResult> failedResults = results.stream()
                    .filter(result -> result.getType() == MigrationResultType.FAILED)
                    .limit(50)
                    .collect(Collectors.toList());

            if (!failedResults.isEmpty()) {
                report.append("失败记录详情 (前50条):\n");
                for (MigrationResult result : failedResults) {
                    report.append("- 记录ID: ").append(result.getRecordId())
                            .append(", 原因: ").append(result.getMessage()).append("\n");
                }
            }
        }

        return report.toString();
    }
}
