package com.jiao.data.strategy.impl;

import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.model.DataRecord;
import com.jiao.data.strategy.DataCompareStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用数据对比策略实现
 */
@Slf4j
@Component
public class GenericDataCompareStrategy implements DataCompareStrategy {
    
    @Override
    public String getStrategyName() {
        return "GenericDataCompareStrategy";
    }
    
    @Override
    public boolean supports(String sourceType, String targetType) {
        // 支持所有数据源类型的对比
        return true;
    }
    
    @Override
    public List<CompareResult> compare(DataSourceAdapter sourceAdapter,
                                     DataSourceAdapter targetAdapter,
                                     String sourceTable,
                                     String targetTable,
                                     Map<String, Object> compareConfig) {
        
        log.info("开始对比数据: {} -> {}", sourceAdapter.getDataSourceType(), targetAdapter.getDataSourceType());
        
        List<CompareResult> results = new ArrayList<>();
        
        try {
            // 获取配置参数
            int batchSize = (Integer) compareConfig.getOrDefault("batchSize", 1000);
            List<String> ignoreFields = (List<String>) compareConfig.getOrDefault("ignoreFields", new ArrayList<>());
            boolean deepCompare = (Boolean) compareConfig.getOrDefault("deepCompare", true);
            
            // 分批对比数据
            long sourceCount = sourceAdapter.count(sourceTable);
            long targetCount = targetAdapter.count(targetTable);
            
            log.info("源表记录数: {}, 目标表记录数: {}", sourceCount, targetCount);
            
            // 构建目标数据的索引
            Map<String, DataRecord> targetRecordMap = buildTargetRecordMap(targetAdapter, targetTable, batchSize);
            
            // 分批处理源数据
            for (int offset = 0; offset < sourceCount; offset += batchSize) {
                List<DataRecord> sourceRecords = sourceAdapter.queryData(sourceTable, offset, batchSize);
                
                for (DataRecord sourceRecord : sourceRecords) {
                    String recordId = sourceRecord.getId();
                    DataRecord targetRecord = targetRecordMap.get(recordId);
                    
                    CompareResult result = compareRecords(sourceRecord, targetRecord, compareConfig);
                    results.add(result);
                    
                    // 从目标记录映射中移除已处理的记录
                    if (targetRecord != null) {
                        targetRecordMap.remove(recordId);
                    }
                }
            }
            
            // 处理仅在目标端存在的记录
            for (DataRecord targetRecord : targetRecordMap.values()) {
                CompareResult result = new CompareResult(
                    targetRecord.getId(),
                    CompareResultType.TARGET_ONLY,
                    null,
                    targetRecord,
                    null
                );
                results.add(result);
            }
            
        } catch (Exception e) {
            log.error("数据对比过程中发生错误", e);
        }
        
        log.info("数据对比完成，共对比 {} 条记录", results.size());
        return results;
    }
    
    private Map<String, DataRecord> buildTargetRecordMap(DataSourceAdapter targetAdapter, 
                                                        String targetTable, 
                                                        int batchSize) {
        Map<String, DataRecord> targetRecordMap = new HashMap<>();
        
        long targetCount = targetAdapter.count(targetTable);
        for (int offset = 0; offset < targetCount; offset += batchSize) {
            List<DataRecord> targetRecords = targetAdapter.queryData(targetTable, offset, batchSize);
            
            for (DataRecord record : targetRecords) {
                if (record.getId() != null) {
                    targetRecordMap.put(record.getId(), record);
                }
            }
        }
        
        return targetRecordMap;
    }
    
    @Override
    public CompareResult compareRecords(DataRecord sourceRecord,
                                      DataRecord targetRecord,
                                      Map<String, Object> compareConfig) {
        
        if (sourceRecord == null && targetRecord == null) {
            return null;
        }
        
        String recordId = sourceRecord != null ? sourceRecord.getId() : targetRecord.getId();
        
        // 仅源端存在
        if (sourceRecord != null && targetRecord == null) {
            return new CompareResult(recordId, CompareResultType.SOURCE_ONLY, sourceRecord, null, null);
        }
        
        // 仅目标端存在
        if (sourceRecord == null && targetRecord != null) {
            return new CompareResult(recordId, CompareResultType.TARGET_ONLY, null, targetRecord, null);
        }
        
        // 两端都存在，进行详细对比
        Map<String, Object> differences = compareRecordData(sourceRecord, targetRecord, compareConfig);
        
        CompareResultType resultType = differences.isEmpty() ? 
            CompareResultType.IDENTICAL : CompareResultType.DIFFERENT;
        
        return new CompareResult(recordId, resultType, sourceRecord, targetRecord, differences);
    }
    
    private Map<String, Object> compareRecordData(DataRecord sourceRecord,
                                                 DataRecord targetRecord,
                                                 Map<String, Object> compareConfig) {
        
        Map<String, Object> differences = new HashMap<>();
        
        List<String> ignoreFields = (List<String>) compareConfig.getOrDefault("ignoreFields", new ArrayList<>());
        boolean deepCompare = (Boolean) compareConfig.getOrDefault("deepCompare", true);
        
        Map<String, Object> sourceData = sourceRecord.getData();
        Map<String, Object> targetData = targetRecord.getData();
        
        if (sourceData == null) sourceData = new HashMap<>();
        if (targetData == null) targetData = new HashMap<>();
        
        // 获取所有字段名
        Set<String> allFields = new HashSet<>();
        allFields.addAll(sourceData.keySet());
        allFields.addAll(targetData.keySet());
        
        // 移除忽略的字段
        allFields.removeAll(ignoreFields);
        
        for (String field : allFields) {
            Object sourceValue = sourceData.get(field);
            Object targetValue = targetData.get(field);
            
            if (!Objects.equals(sourceValue, targetValue)) {
                Map<String, Object> fieldDiff = new HashMap<>();
                fieldDiff.put("source", sourceValue);
                fieldDiff.put("target", targetValue);
                differences.put(field, fieldDiff);
            }
        }
        
        return differences;
    }
    
    @Override
    public String generateCompareReport(List<CompareResult> compareResults) {
        if (compareResults == null || compareResults.isEmpty()) {
            return "对比结果为空";
        }
        
        StringBuilder report = new StringBuilder();
        report.append("=== 数据对比报告 ===\n");
        report.append("生成时间: ").append(new Date()).append("\n\n");
        
        // 统计信息
        Map<CompareResultType, Long> statistics = compareResults.stream()
            .collect(Collectors.groupingBy(CompareResult::getType, Collectors.counting()));
        
        report.append("统计信息:\n");
        report.append("- 总记录数: ").append(compareResults.size()).append("\n");
        report.append("- 完全相同: ").append(statistics.getOrDefault(CompareResultType.IDENTICAL, 0L)).append("\n");
        report.append("- 存在差异: ").append(statistics.getOrDefault(CompareResultType.DIFFERENT, 0L)).append("\n");
        report.append("- 仅源端存在: ").append(statistics.getOrDefault(CompareResultType.SOURCE_ONLY, 0L)).append("\n");
        report.append("- 仅目标端存在: ").append(statistics.getOrDefault(CompareResultType.TARGET_ONLY, 0L)).append("\n\n");
        
        // 详细差异信息（仅显示前100条）
        List<CompareResult> differentResults = compareResults.stream()
            .filter(result -> result.getType() != CompareResultType.IDENTICAL)
            .limit(100)
            .collect(Collectors.toList());
        
        if (!differentResults.isEmpty()) {
            report.append("差异详情 (前100条):\n");
            for (CompareResult result : differentResults) {
                report.append("- 记录ID: ").append(result.getRecordId())
                      .append(", 类型: ").append(result.getType()).append("\n");
                
                if (result.getDifferences() != null && !result.getDifferences().isEmpty()) {
                    for (Map.Entry<String, Object> diff : result.getDifferences().entrySet()) {
                        report.append("  字段: ").append(diff.getKey())
                              .append(", 差异: ").append(diff.getValue()).append("\n");
                    }
                }
                report.append("\n");
            }
        }
        
        return report.toString();
    }
}
