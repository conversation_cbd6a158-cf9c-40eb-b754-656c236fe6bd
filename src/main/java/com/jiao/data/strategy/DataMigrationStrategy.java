package com.jiao.data.strategy;

import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.model.DataRecord;

import java.util.List;
import java.util.Map;

/**
 * 数据迁移策略接口
 */
public interface DataMigrationStrategy extends DataStrategy {
    
    /**
     * 迁移结果类型
     */
    enum MigrationResultType {
        SUCCESS,        // 成功
        FAILED,         // 失败
        SKIPPED,        // 跳过
        PARTIAL_SUCCESS // 部分成功
    }
    
    /**
     * 迁移结果
     */
    class MigrationResult {
        private final String recordId;
        private final MigrationResultType type;
        private final String message;
        private final DataRecord sourceRecord;
        private final DataRecord targetRecord;
        private final Exception exception;
        
        public MigrationResult(String recordId, MigrationResultType type, String message,
                             DataRecord sourceRecord, DataRecord targetRecord, Exception exception) {
            this.recordId = recordId;
            this.type = type;
            this.message = message;
            this.sourceRecord = sourceRecord;
            this.targetRecord = targetRecord;
            this.exception = exception;
        }
        
        // Getters
        public String getRecordId() { return recordId; }
        public MigrationResultType getType() { return type; }
        public String getMessage() { return message; }
        public DataRecord getSourceRecord() { return sourceRecord; }
        public DataRecord getTargetRecord() { return targetRecord; }
        public Exception getException() { return exception; }
    }
    
    /**
     * 迁移统计信息
     */
    class MigrationStatistics {
        private long totalRecords;
        private long successCount;
        private long failedCount;
        private long skippedCount;
        private long startTime;
        private long endTime;
        
        // Getters and Setters
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        
        public long getSuccessCount() { return successCount; }
        public void setSuccessCount(long successCount) { this.successCount = successCount; }
        
        public long getFailedCount() { return failedCount; }
        public void setFailedCount(long failedCount) { this.failedCount = failedCount; }
        
        public long getSkippedCount() { return skippedCount; }
        public void setSkippedCount(long skippedCount) { this.skippedCount = skippedCount; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getDuration() { return endTime - startTime; }
    }
    
    /**
     * 执行数据迁移
     * 
     * @param sourceAdapter 源数据源适配器
     * @param targetAdapter 目标数据源适配器
     * @param sourceTable 源表名
     * @param targetTable 目标表名
     * @param migrationConfig 迁移配置
     * @return 迁移统计信息
     */
    MigrationStatistics migrate(DataSourceAdapter sourceAdapter,
                               DataSourceAdapter targetAdapter,
                               String sourceTable,
                               String targetTable,
                               Map<String, Object> migrationConfig);
    
    /**
     * 迁移单条记录
     * 
     * @param sourceRecord 源记录
     * @param targetAdapter 目标数据源适配器
     * @param targetTable 目标表名
     * @param migrationConfig 迁移配置
     * @return 迁移结果
     */
    MigrationResult migrateRecord(DataRecord sourceRecord,
                                 DataSourceAdapter targetAdapter,
                                 String targetTable,
                                 Map<String, Object> migrationConfig);
    
    /**
     * 批量迁移记录
     * 
     * @param sourceRecords 源记录列表
     * @param targetAdapter 目标数据源适配器
     * @param targetTable 目标表名
     * @param migrationConfig 迁移配置
     * @return 迁移结果列表
     */
    List<MigrationResult> batchMigrateRecords(List<DataRecord> sourceRecords,
                                             DataSourceAdapter targetAdapter,
                                             String targetTable,
                                             Map<String, Object> migrationConfig);
    
    /**
     * 转换数据记录以适应目标数据源
     * 
     * @param sourceRecord 源记录
     * @param targetAdapter 目标数据源适配器
     * @param migrationConfig 迁移配置
     * @return 转换后的记录
     */
    DataRecord transformRecord(DataRecord sourceRecord,
                              DataSourceAdapter targetAdapter,
                              Map<String, Object> migrationConfig);
    
    /**
     * 生成迁移报告
     * 
     * @param statistics 迁移统计信息
     * @param results 迁移结果列表
     * @return 迁移报告
     */
    String generateMigrationReport(MigrationStatistics statistics, List<MigrationResult> results);
}
