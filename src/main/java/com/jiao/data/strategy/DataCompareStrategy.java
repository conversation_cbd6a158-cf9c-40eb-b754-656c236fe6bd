package com.jiao.data.strategy;

import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.model.DataRecord;

import java.util.List;
import java.util.Map;

/**
 * 数据对比策略接口
 */
public interface DataCompareStrategy extends DataStrategy {
    
    /**
     * 对比结果类型
     */
    enum CompareResultType {
        IDENTICAL,      // 完全相同
        DIFFERENT,      // 存在差异
        SOURCE_ONLY,    // 仅源端存在
        TARGET_ONLY     // 仅目标端存在
    }
    
    /**
     * 对比结果
     */
    class CompareResult {
        private final String recordId;
        private final CompareResultType type;
        private final DataRecord sourceRecord;
        private final DataRecord targetRecord;
        private final Map<String, Object> differences;
        
        public CompareResult(String recordId, CompareResultType type, 
                           DataRecord sourceRecord, DataRecord targetRecord,
                           Map<String, Object> differences) {
            this.recordId = recordId;
            this.type = type;
            this.sourceRecord = sourceRecord;
            this.targetRecord = targetRecord;
            this.differences = differences;
        }
        
        // Getters
        public String getRecordId() { return recordId; }
        public CompareResultType getType() { return type; }
        public DataRecord getSourceRecord() { return sourceRecord; }
        public DataRecord getTargetRecord() { return targetRecord; }
        public Map<String, Object> getDifferences() { return differences; }
    }
    
    /**
     * 对比两个数据源的指定表/集合
     * 
     * @param sourceAdapter 源数据源适配器
     * @param targetAdapter 目标数据源适配器
     * @param sourceTable 源表名
     * @param targetTable 目标表名
     * @param compareConfig 对比配置
     * @return 对比结果列表
     */
    List<CompareResult> compare(DataSourceAdapter sourceAdapter, 
                               DataSourceAdapter targetAdapter,
                               String sourceTable, 
                               String targetTable,
                               Map<String, Object> compareConfig);
    
    /**
     * 对比两条记录
     * 
     * @param sourceRecord 源记录
     * @param targetRecord 目标记录
     * @param compareConfig 对比配置
     * @return 对比结果
     */
    CompareResult compareRecords(DataRecord sourceRecord, 
                                DataRecord targetRecord,
                                Map<String, Object> compareConfig);
    
    /**
     * 生成对比报告
     * 
     * @param compareResults 对比结果列表
     * @return 对比报告
     */
    String generateCompareReport(List<CompareResult> compareResults);
}
