package com.jiao.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通用数据记录模型
 * 用于在不同数据源之间传递数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataRecord {

    /**
     * 记录的唯一标识
     */
    private String id;

    /**
     * 数据内容，使用Map存储键值对
     */
    private Map<String, Object> data;

    /**
     * 数据源类型
     */
    private String sourceType;

    /**
     * 表名或集合名
     */
    private String tableName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 获取指定字段的值
     */
    public Object getValue(String fieldName) {
        return data != null ? data.get(fieldName) : null;
    }

    /**
     * 设置字段值
     */
    public void setValue(String fieldName, Object value) {
        if (data == null) {
            data = new java.util.HashMap<>();
        }
        data.put(fieldName, value);
    }

    /**
     * 检查是否包含指定字段
     */
    public boolean hasField(String fieldName) {
        return data != null && data.containsKey(fieldName);
    }
}
