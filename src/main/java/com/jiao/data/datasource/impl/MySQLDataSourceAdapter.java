package com.jiao.data.datasource.impl;

import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.model.DataRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;

/**
 * MySQL数据源适配器实现
 */
@Slf4j
public class MySQLDataSourceAdapter implements DataSourceAdapter {
    
    private final DataSource dataSource;
    private final String databaseName;
    
    public MySQLDataSourceAdapter(DataSource dataSource, String databaseName) {
        this.dataSource = dataSource;
        this.databaseName = databaseName;
    }
    
    @Override
    public String getDataSourceType() {
        return "MySQL";
    }
    
    @Override
    public boolean testConnection() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (SQLException e) {
            log.error("MySQL连接测试失败", e);
            return false;
        }
    }
    
    @Override
    public List<String> getTableNames() {
        List<String> tableNames = new ArrayList<>();
        String sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = ?";
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            stmt.setString(1, databaseName);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    tableNames.add(rs.getString("table_name"));
                }
            }
        } catch (SQLException e) {
            log.error("获取MySQL表名失败", e);
        }
        
        return tableNames;
    }
    
    @Override
    public Map<String, String> getTableSchema(String tableName) {
        Map<String, String> schema = new LinkedHashMap<>();
        String sql = "SELECT column_name, data_type FROM information_schema.columns " +
                    "WHERE table_schema = ? AND table_name = ? ORDER BY ordinal_position";
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            stmt.setString(1, databaseName);
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    schema.put(rs.getString("column_name"), rs.getString("data_type"));
                }
            }
        } catch (SQLException e) {
            log.error("获取MySQL表结构失败: {}", tableName, e);
        }
        
        return schema;
    }
    
    @Override
    public long count(String tableName) {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getLong(1);
            }
        } catch (SQLException e) {
            log.error("查询MySQL表记录数失败: {}", tableName, e);
        }
        
        return 0;
    }
    
    @Override
    public List<DataRecord> queryData(String tableName, int offset, int limit) {
        List<DataRecord> records = new ArrayList<>();
        String sql = "SELECT * FROM " + tableName + " LIMIT ? OFFSET ?";
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            stmt.setInt(1, limit);
            stmt.setInt(2, offset);
            
            try (ResultSet rs = stmt.executeQuery()) {
                records = convertResultSetToDataRecords(rs, tableName);
            }
        } catch (SQLException e) {
            log.error("分页查询MySQL数据失败: {}", tableName, e);
        }
        
        return records;
    }
    
    @Override
    public List<DataRecord> queryData(String tableName, Map<String, Object> conditions) {
        List<DataRecord> records = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT * FROM ").append(tableName);
        
        if (conditions != null && !conditions.isEmpty()) {
            sql.append(" WHERE ");
            List<String> whereClauses = new ArrayList<>();
            for (String key : conditions.keySet()) {
                whereClauses.add(key + " = ?");
            }
            sql.append(String.join(" AND ", whereClauses));
        }
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
            
            if (conditions != null && !conditions.isEmpty()) {
                int paramIndex = 1;
                for (Object value : conditions.values()) {
                    stmt.setObject(paramIndex++, value);
                }
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                records = convertResultSetToDataRecords(rs, tableName);
            }
        } catch (SQLException e) {
            log.error("条件查询MySQL数据失败: {}", tableName, e);
        }
        
        return records;
    }
    
    @Override
    public DataRecord queryById(String tableName, String id) {
        // 这里假设主键字段名为id，实际使用时可能需要动态获取主键字段名
        Map<String, Object> conditions = new HashMap<>();
        conditions.put("id", id);
        
        List<DataRecord> records = queryData(tableName, conditions);
        return records.isEmpty() ? null : records.get(0);
    }
    
    private List<DataRecord> convertResultSetToDataRecords(ResultSet rs, String tableName) throws SQLException {
        List<DataRecord> records = new ArrayList<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();
        
        while (rs.next()) {
            Map<String, Object> data = new HashMap<>();
            String recordId = null;
            
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object value = rs.getObject(i);
                data.put(columnName, value);
                
                // 尝试获取ID字段
                if ("id".equalsIgnoreCase(columnName) && recordId == null) {
                    recordId = String.valueOf(value);
                }
            }
            
            DataRecord record = DataRecord.builder()
                    .id(recordId)
                    .data(data)
                    .sourceType(getDataSourceType())
                    .tableName(tableName)
                    .createTime(LocalDateTime.now())
                    .build();
            
            records.add(record);
        }
        
        return records;
    }
    
    @Override
    public boolean insert(String tableName, DataRecord record) {
        return batchInsert(tableName, Arrays.asList(record));
    }
    
    @Override
    public boolean batchInsert(String tableName, List<DataRecord> records) {
        if (records == null || records.isEmpty()) {
            return true;
        }
        
        DataRecord firstRecord = records.get(0);
        if (firstRecord.getData() == null || firstRecord.getData().isEmpty()) {
            return false;
        }
        
        Set<String> columns = firstRecord.getData().keySet();
        String sql = buildInsertSql(tableName, columns);
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            for (DataRecord record : records) {
                int paramIndex = 1;
                for (String column : columns) {
                    stmt.setObject(paramIndex++, record.getValue(column));
                }
                stmt.addBatch();
            }
            
            int[] results = stmt.executeBatch();
            return Arrays.stream(results).allMatch(result -> result > 0);
            
        } catch (SQLException e) {
            log.error("批量插入MySQL数据失败: {}", tableName, e);
            return false;
        }
    }
    
    private String buildInsertSql(String tableName, Set<String> columns) {
        StringBuilder sql = new StringBuilder("INSERT INTO ").append(tableName).append(" (");
        sql.append(String.join(", ", columns));
        sql.append(") VALUES (");
        sql.append(String.join(", ", Collections.nCopies(columns.size(), "?")));
        sql.append(")");
        return sql.toString();
    }
    
    @Override
    public boolean update(String tableName, DataRecord record) {
        return batchUpdate(tableName, Arrays.asList(record));
    }
    
    @Override
    public boolean batchUpdate(String tableName, List<DataRecord> records) {
        // 简化实现，实际使用时需要根据主键进行更新
        log.warn("MySQL批量更新功能需要根据具体业务场景实现");
        return false;
    }
    
    @Override
    public boolean delete(String tableName, String id) {
        return batchDelete(tableName, Arrays.asList(id));
    }
    
    @Override
    public boolean batchDelete(String tableName, List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }
        
        String sql = "DELETE FROM " + tableName + " WHERE id IN (" +
                    String.join(", ", Collections.nCopies(ids.size(), "?")) + ")";
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            for (int i = 0; i < ids.size(); i++) {
                stmt.setString(i + 1, ids.get(i));
            }
            
            int result = stmt.executeUpdate();
            return result > 0;
            
        } catch (SQLException e) {
            log.error("批量删除MySQL数据失败: {}", tableName, e);
            return false;
        }
    }
    
    @Override
    public List<DataRecord> executeQuery(String query, Map<String, Object> parameters) {
        List<DataRecord> records = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection();
             PreparedStatement stmt = connection.prepareStatement(query)) {
            
            if (parameters != null && !parameters.isEmpty()) {
                int paramIndex = 1;
                for (Object value : parameters.values()) {
                    stmt.setObject(paramIndex++, value);
                }
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                records = convertResultSetToDataRecords(rs, "custom_query");
            }
        } catch (SQLException e) {
            log.error("执行自定义MySQL查询失败", e);
        }
        
        return records;
    }
    
    @Override
    public void close() {
        // DataSource通常由Spring管理，这里不需要关闭
        log.info("MySQL数据源适配器关闭");
    }
}
