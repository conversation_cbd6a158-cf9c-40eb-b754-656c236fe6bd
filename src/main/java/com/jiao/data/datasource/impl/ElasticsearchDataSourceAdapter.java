package com.jiao.data.datasource.impl;

import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.model.DataRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.core.query.StringQuery;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Elasticsearch数据源适配器实现
 */
@Slf4j
public class ElasticsearchDataSourceAdapter implements DataSourceAdapter {

    private final ElasticsearchTemplate elasticsearchTemplate;

    public ElasticsearchDataSourceAdapter(ElasticsearchTemplate elasticsearchTemplate) {
        this.elasticsearchTemplate = elasticsearchTemplate;
    }

    @Override
    public String getDataSourceType() {
        return "Elasticsearch";
    }

    @Override
    public boolean testConnection() {
        try {
            return elasticsearchTemplate.indexOps(org.springframework.data.elasticsearch.core.IndexOperations.class)
                    .exists();
        } catch (Exception e) {
            log.error("Elasticsearch连接测试失败", e);
            return false;
        }
    }

    @Override
    public List<String> getTableNames() {
        try {
            // 获取所有索引名称
            Set<String> indexNames = elasticsearchTemplate.indexOps(org.springframework.data.elasticsearch.core.IndexOperations.class)
                    .getMapping().keySet();
            return new ArrayList<>(indexNames);
        } catch (Exception e) {
            log.error("获取Elasticsearch索引名称失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, String> getTableSchema(String tableName) {
        Map<String, String> schema = new HashMap<>();
        try {
            // 获取索引的映射信息
            var indexOps = elasticsearchTemplate.indexOps(IndexCoordinates.of(tableName));
            if (indexOps.exists()) {
                var mapping = indexOps.getMapping();
                // 简化处理，实际使用时需要解析复杂的映射结构
                schema.put("_id", "keyword");
                schema.put("_source", "object");
            }
        } catch (Exception e) {
            log.error("获取Elasticsearch索引映射失败: {}", tableName, e);
        }
        return schema;
    }

    @Override
    public long count(String tableName) {
        try {
            Query query = new StringQuery("*:*");
            return elasticsearchTemplate.count(query, IndexCoordinates.of(tableName));
        } catch (Exception e) {
            log.error("查询Elasticsearch索引记录数失败: {}", tableName, e);
            return 0;
        }
    }

    @Override
    public List<DataRecord> queryData(String tableName, int offset, int limit) {
        try {
            String queryString = String.format("""
                    {
                        "query": {"match_all": {}},
                        "from": %d,
                        "size": %d
                    }
                    """, offset, limit);

            Query query = new StringQuery(queryString);
            SearchHits<Map> searchHits = elasticsearchTemplate.search(query, Map.class, IndexCoordinates.of(tableName));

            return convertSearchHitsToDataRecords(searchHits, tableName);
        } catch (Exception e) {
            log.error("分页查询Elasticsearch数据失败: {}", tableName, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<DataRecord> queryData(String tableName, Map<String, Object> conditions) {
        try {
            StringBuilder queryBuilder = new StringBuilder();
            queryBuilder.append("{\"query\":{");

            if (conditions == null || conditions.isEmpty()) {
                queryBuilder.append("\"match_all\":{}");
            } else {
                queryBuilder.append("\"bool\":{\"must\":[");
                List<String> mustClauses = new ArrayList<>();

                for (Map.Entry<String, Object> condition : conditions.entrySet()) {
                    String field = condition.getKey();
                    Object value = condition.getValue();
                    mustClauses.add(String.format("{\"term\":{\"%s\":\"%s\"}}", field, value));
                }

                queryBuilder.append(String.join(",", mustClauses));
                queryBuilder.append("]}");
            }

            queryBuilder.append("}}");

            Query query = new StringQuery(queryBuilder.toString());
            SearchHits<Map> searchHits = elasticsearchTemplate.search(query, Map.class,
                    IndexCoordinates.of(tableName));

            return convertSearchHitsToDataRecords(searchHits, tableName);
        } catch (Exception e) {
            log.error("条件查询Elasticsearch数据失败: {}", tableName, e);
            return new ArrayList<>();
        }
    }

    @Override
    public DataRecord queryById(String tableName, String id) {
        try {
            Map<String, Object> document = elasticsearchTemplate.get(id, Map.class,
                    IndexCoordinates.of(tableName));

            if (document != null) {
                return DataRecord.builder()
                        .id(id)
                        .data(document)
                        .sourceType(getDataSourceType())
                        .tableName(tableName)
                        .createTime(LocalDateTime.now())
                        .build();
            }
        } catch (Exception e) {
            log.error("根据ID查询Elasticsearch数据失败: {} - {}", tableName, id, e);
        }

        return null;
    }

    private List<DataRecord> convertSearchHitsToDataRecords(SearchHits<Map> searchHits, String tableName) {
        return searchHits.getSearchHits().stream()
                .map(hit -> convertSearchHitToDataRecord(hit, tableName))
                .collect(Collectors.toList());
    }

    private DataRecord convertSearchHitToDataRecord(SearchHit<Map> hit, String tableName) {
        Map<String, Object> source = hit.getContent();

        return DataRecord.builder()
                .id(hit.getId())
                .data(source)
                .sourceType(getDataSourceType())
                .tableName(tableName)
                .createTime(LocalDateTime.now())
                .build();
    }

    @Override
    public boolean insert(String tableName, DataRecord record) {
        return batchInsert(tableName, Arrays.asList(record));
    }

    @Override
    public boolean batchInsert(String tableName, List<DataRecord> records) {
        try {
            for (DataRecord record : records) {
                elasticsearchTemplate.save(record.getData(), IndexCoordinates.of(tableName));
            }
            return true;
        } catch (Exception e) {
            log.error("批量插入Elasticsearch数据失败: {}", tableName, e);
            return false;
        }
    }

    @Override
    public boolean update(String tableName, DataRecord record) {
        try {
            elasticsearchTemplate.save(record.getData(), IndexCoordinates.of(tableName));
            return true;
        } catch (Exception e) {
            log.error("更新Elasticsearch数据失败: {}", tableName, e);
            return false;
        }
    }

    @Override
    public boolean batchUpdate(String tableName, List<DataRecord> records) {
        return batchInsert(tableName, records); // ES中更新和插入操作相同
    }

    @Override
    public boolean delete(String tableName, String id) {
        try {
            String result = elasticsearchTemplate.delete(id, IndexCoordinates.of(tableName));
            return result != null;
        } catch (Exception e) {
            log.error("删除Elasticsearch数据失败: {} - {}", tableName, id, e);
            return false;
        }
    }

    @Override
    public boolean batchDelete(String tableName, List<String> ids) {
        try {
            for (String id : ids) {
                elasticsearchTemplate.delete(id, IndexCoordinates.of(tableName));
            }
            return true;
        } catch (Exception e) {
            log.error("批量删除Elasticsearch数据失败: {}", tableName, e);
            return false;
        }
    }

    @Override
    public List<DataRecord> executeQuery(String query, Map<String, Object> parameters) {
        try {
            // 如果parameters中包含index，使用指定的索引
            String indexName = parameters != null ?
                    (String) parameters.getOrDefault("index", "default_index") : "default_index";

            Query esQuery = new StringQuery(query);
            SearchHits<Map> searchHits = elasticsearchTemplate.search(esQuery, Map.class, IndexCoordinates.of(indexName));

            return convertSearchHitsToDataRecords(searchHits, indexName);
        } catch (Exception e) {
            log.error("执行自定义Elasticsearch查询失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public void close() {
        log.info("Elasticsearch数据源适配器关闭");
    }
}
