package com.jiao.data.datasource.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jiao.data.datasource.DataSourceAdapter;
import com.jiao.data.model.DataRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.Cursor;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Redis数据源适配器实现
 */
@Slf4j
public class RedisDataSourceAdapter implements DataSourceAdapter {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    private final String keyPrefix;
    
    public RedisDataSourceAdapter(RedisTemplate<String, Object> redisTemplate, String keyPrefix) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = new ObjectMapper();
        this.keyPrefix = keyPrefix != null ? keyPrefix : "";
    }
    
    @Override
    public String getDataSourceType() {
        return "Redis";
    }
    
    @Override
    public boolean testConnection() {
        try {
            String pong = redisTemplate.getConnectionFactory().getConnection().ping();
            return "PONG".equals(pong);
        } catch (Exception e) {
            log.error("Redis连接测试失败", e);
            return false;
        }
    }
    
    @Override
    public List<String> getTableNames() {
        // Redis中没有表的概念，这里返回不同的key前缀作为"表名"
        Set<String> keys = redisTemplate.keys(keyPrefix + "*");
        if (keys == null) {
            return new ArrayList<>();
        }
        
        return keys.stream()
                .map(key -> key.startsWith(keyPrefix) ? key.substring(keyPrefix.length()) : key)
                .map(key -> key.contains(":") ? key.substring(0, key.indexOf(":")) : key)
                .distinct()
                .collect(Collectors.toList());
    }
    
    @Override
    public Map<String, String> getTableSchema(String tableName) {
        // Redis是NoSQL，没有固定schema，返回空Map
        Map<String, String> schema = new HashMap<>();
        schema.put("key", "String");
        schema.put("value", "Object");
        return schema;
    }
    
    @Override
    public long count(String tableName) {
        String pattern = keyPrefix + tableName + ":*";
        Set<String> keys = redisTemplate.keys(pattern);
        return keys != null ? keys.size() : 0;
    }
    
    @Override
    public List<DataRecord> queryData(String tableName, int offset, int limit) {
        List<DataRecord> records = new ArrayList<>();
        String pattern = keyPrefix + tableName + ":*";
        
        try (Cursor<String> cursor = redisTemplate.scan(ScanOptions.scanOptions()
                .match(pattern)
                .count(1000)
                .build())) {
            
            int currentIndex = 0;
            int collected = 0;
            
            while (cursor.hasNext() && collected < limit) {
                String key = cursor.next();
                
                if (currentIndex >= offset) {
                    Object value = redisTemplate.opsForValue().get(key);
                    DataRecord record = convertToDataRecord(key, value, tableName);
                    if (record != null) {
                        records.add(record);
                        collected++;
                    }
                }
                currentIndex++;
            }
        } catch (Exception e) {
            log.error("分页查询Redis数据失败: {}", tableName, e);
        }
        
        return records;
    }
    
    @Override
    public List<DataRecord> queryData(String tableName, Map<String, Object> conditions) {
        List<DataRecord> records = new ArrayList<>();
        
        if (conditions != null && conditions.containsKey("key")) {
            // 如果条件中包含key，直接查询
            String key = keyPrefix + tableName + ":" + conditions.get("key");
            Object value = redisTemplate.opsForValue().get(key);
            DataRecord record = convertToDataRecord(key, value, tableName);
            if (record != null) {
                records.add(record);
            }
        } else {
            // 否则扫描所有匹配的key
            String pattern = keyPrefix + tableName + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            
            if (keys != null) {
                for (String key : keys) {
                    Object value = redisTemplate.opsForValue().get(key);
                    DataRecord record = convertToDataRecord(key, value, tableName);
                    if (record != null && matchesConditions(record, conditions)) {
                        records.add(record);
                    }
                }
            }
        }
        
        return records;
    }
    
    @Override
    public DataRecord queryById(String tableName, String id) {
        String key = keyPrefix + tableName + ":" + id;
        Object value = redisTemplate.opsForValue().get(key);
        return convertToDataRecord(key, value, tableName);
    }
    
    private DataRecord convertToDataRecord(String key, Object value, String tableName) {
        if (value == null) {
            return null;
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("key", key);
        
        // 尝试将value转换为Map
        if (value instanceof Map) {
            data.putAll((Map<String, Object>) value);
        } else if (value instanceof String) {
            try {
                // 尝试解析JSON字符串
                Map<String, Object> jsonMap = objectMapper.readValue((String) value, Map.class);
                data.putAll(jsonMap);
            } catch (JsonProcessingException e) {
                // 如果不是JSON，直接存储为value字段
                data.put("value", value);
            }
        } else {
            data.put("value", value);
        }
        
        // 从key中提取ID
        String id = key;
        if (key.startsWith(keyPrefix + tableName + ":")) {
            id = key.substring((keyPrefix + tableName + ":").length());
        }
        
        return DataRecord.builder()
                .id(id)
                .data(data)
                .sourceType(getDataSourceType())
                .tableName(tableName)
                .createTime(LocalDateTime.now())
                .build();
    }
    
    private boolean matchesConditions(DataRecord record, Map<String, Object> conditions) {
        if (conditions == null || conditions.isEmpty()) {
            return true;
        }
        
        for (Map.Entry<String, Object> condition : conditions.entrySet()) {
            String fieldName = condition.getKey();
            Object expectedValue = condition.getValue();
            Object actualValue = record.getValue(fieldName);
            
            if (!Objects.equals(expectedValue, actualValue)) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public boolean insert(String tableName, DataRecord record) {
        return batchInsert(tableName, Arrays.asList(record));
    }
    
    @Override
    public boolean batchInsert(String tableName, List<DataRecord> records) {
        try {
            for (DataRecord record : records) {
                String key = keyPrefix + tableName + ":" + record.getId();
                
                // 移除key字段，避免重复存储
                Map<String, Object> data = new HashMap<>(record.getData());
                data.remove("key");
                
                if (data.size() == 1 && data.containsKey("value")) {
                    // 如果只有value字段，直接存储value
                    redisTemplate.opsForValue().set(key, data.get("value"));
                } else {
                    // 否则存储整个Map
                    redisTemplate.opsForValue().set(key, data);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量插入Redis数据失败: {}", tableName, e);
            return false;
        }
    }
    
    @Override
    public boolean update(String tableName, DataRecord record) {
        // Redis中更新和插入是相同的操作
        return insert(tableName, record);
    }
    
    @Override
    public boolean batchUpdate(String tableName, List<DataRecord> records) {
        // Redis中更新和插入是相同的操作
        return batchInsert(tableName, records);
    }
    
    @Override
    public boolean delete(String tableName, String id) {
        return batchDelete(tableName, Arrays.asList(id));
    }
    
    @Override
    public boolean batchDelete(String tableName, List<String> ids) {
        try {
            List<String> keys = ids.stream()
                    .map(id -> keyPrefix + tableName + ":" + id)
                    .collect(Collectors.toList());
            
            Long deletedCount = redisTemplate.delete(keys);
            return deletedCount != null && deletedCount > 0;
        } catch (Exception e) {
            log.error("批量删除Redis数据失败: {}", tableName, e);
            return false;
        }
    }
    
    @Override
    public List<DataRecord> executeQuery(String query, Map<String, Object> parameters) {
        // Redis不支持SQL查询，这里可以实现一些简单的查询逻辑
        log.warn("Redis不支持SQL查询，query参数将被忽略: {}", query);
        
        if (parameters != null && parameters.containsKey("pattern")) {
            String pattern = (String) parameters.get("pattern");
            Set<String> keys = redisTemplate.keys(pattern);
            
            List<DataRecord> records = new ArrayList<>();
            if (keys != null) {
                for (String key : keys) {
                    Object value = redisTemplate.opsForValue().get(key);
                    DataRecord record = convertToDataRecord(key, value, "custom_query");
                    if (record != null) {
                        records.add(record);
                    }
                }
            }
            return records;
        }
        
        return new ArrayList<>();
    }
    
    @Override
    public void close() {
        log.info("Redis数据源适配器关闭");
    }
}
