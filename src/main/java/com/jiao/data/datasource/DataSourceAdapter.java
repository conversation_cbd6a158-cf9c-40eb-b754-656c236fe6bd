package com.jiao.data.datasource;

import com.jiao.data.model.DataRecord;

import java.util.List;
import java.util.Map;

/**
 * 数据源适配器接口
 * 提供统一的数据访问接口，屏蔽不同数据源的差异
 */
public interface DataSourceAdapter {
    
    /**
     * 获取数据源类型
     */
    String getDataSourceType();
    
    /**
     * 测试连接
     */
    boolean testConnection();
    
    /**
     * 获取所有表/集合名称
     */
    List<String> getTableNames();
    
    /**
     * 获取表结构信息
     */
    Map<String, String> getTableSchema(String tableName);
    
    /**
     * 查询数据总数
     */
    long count(String tableName);
    
    /**
     * 分页查询数据
     */
    List<DataRecord> queryData(String tableName, int offset, int limit);
    
    /**
     * 根据条件查询数据
     */
    List<DataRecord> queryData(String tableName, Map<String, Object> conditions);
    
    /**
     * 根据ID查询单条数据
     */
    DataRecord queryById(String tableName, String id);
    
    /**
     * 插入数据
     */
    boolean insert(String tableName, DataRecord record);
    
    /**
     * 批量插入数据
     */
    boolean batchInsert(String tableName, List<DataRecord> records);
    
    /**
     * 更新数据
     */
    boolean update(String tableName, DataRecord record);
    
    /**
     * 批量更新数据
     */
    boolean batchUpdate(String tableName, List<DataRecord> records);
    
    /**
     * 删除数据
     */
    boolean delete(String tableName, String id);
    
    /**
     * 批量删除数据
     */
    boolean batchDelete(String tableName, List<String> ids);
    
    /**
     * 执行自定义查询
     */
    List<DataRecord> executeQuery(String query, Map<String, Object> parameters);
    
    /**
     * 关闭连接
     */
    void close();
}
