package com.jiao.data.example;

import com.jiao.data.config.SyncConfig;
import com.jiao.data.sync.DataSyncEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数据同步使用示例
 * 演示如何使用通用数据同步策略
 */
@Slf4j
@Component
public class DataSyncExample implements CommandLineRunner {
    
    @Autowired
    private DataSyncEngine dataSyncEngine;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("=== 数据同步示例开始 ===");
        
        // 示例1：MySQL到MySQL的数据对比
        // mysqlToMySQLCompareExample();
        
        // 示例2：MySQL到Redis的数据迁移
        // mysqlToRedisExample();
        
        // 示例3：MySQL到Elasticsearch的数据同步
        // mysqlToElasticsearchExample();
        
        log.info("=== 数据同步示例结束 ===");
    }
    
    /**
     * MySQL到MySQL数据对比示例
     */
    private void mysqlToMySQLCompareExample() {
        log.info("--- MySQL到MySQL数据对比示例 ---");
        
        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("MySQL用户表对比")
                .description("对比两个MySQL数据库中的用户表数据")
                .syncType(SyncConfig.SyncType.COMPARE)
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "*************************************",
                    "root",
                    "password",
                    "source_db",
                    "users"
                ))
                .targetConfig(SyncConfig.createMySQLConfig(
                    "*************************************",
                    "root",
                    "password",
                    "target_db",
                    "users"
                ))
                .batchSize(1000)
                .deepCompare(true)
                .ignoreFields(List.of("create_time", "update_time"))
                .build();
        
        DataSyncEngine.SyncResult result = dataSyncEngine.executeSync(syncConfig);
        
        log.info("对比结果: {}", result.isSuccess() ? "成功" : "失败");
        log.info("对比报告:\n{}", result.getReport());
    }
    
    /**
     * MySQL到Redis数据迁移示例
     */
    private void mysqlToRedisExample() {
        log.info("--- MySQL到Redis数据迁移示例 ---");
        
        // 字段映射配置
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("user_id", "id");
        fieldMapping.put("user_name", "name");
        fieldMapping.put("user_email", "email");
        
        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("MySQL用户数据迁移到Redis")
                .description("将MySQL中的用户数据迁移到Redis缓存")
                .syncType(SyncConfig.SyncType.MIGRATE)
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "**********************************",
                    "root",
                    "password",
                    "app_db",
                    "users"
                ))
                .targetConfig(SyncConfig.createRedisConfig(
                    "redis://localhost:6379",
                    "redis_password",
                    "user"
                ))
                .batchSize(500)
                .skipExisting(true)
                .continueOnError(true)
                .fieldMapping(fieldMapping)
                .ignoreFields(List.of("password", "salt"))
                .build();
        
        // 添加自定义配置
        syncConfig.getCustomConfig().put("keyPrefix", "app:user:");
        
        DataSyncEngine.SyncResult result = dataSyncEngine.executeSync(syncConfig);
        
        log.info("迁移结果: {}", result.isSuccess() ? "成功" : "失败");
        log.info("迁移统计: {}", result.getMigrationStatistics());
        log.info("迁移报告:\n{}", result.getReport());
    }
    
    /**
     * MySQL到Elasticsearch数据同步示例
     */
    private void mysqlToElasticsearchExample() {
        log.info("--- MySQL到Elasticsearch数据同步示例 ---");
        
        // 字段映射配置
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("product_id", "id");
        fieldMapping.put("product_name", "name");
        fieldMapping.put("product_desc", "description");
        fieldMapping.put("category_id", "categoryId");
        fieldMapping.put("create_time", "createTime");
        
        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("MySQL商品数据同步到Elasticsearch")
                .description("将MySQL中的商品数据同步到Elasticsearch搜索引擎")
                .syncType(SyncConfig.SyncType.SYNC)
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "*************************************",
                    "root",
                    "password",
                    "ecommerce",
                    "products"
                ))
                .targetConfig(SyncConfig.createElasticsearchConfig(
                    "http://localhost:9200",
                    "elastic",
                    "elastic_password",
                    "products"
                ))
                .batchSize(1000)
                .skipExisting(false)
                .continueOnError(true)
                .fieldMapping(fieldMapping)
                .deepCompare(true)
                .build();
        
        // 添加自定义配置
        Map<String, Object> customConfig = syncConfig.getCustomConfig();
        customConfig.put("indexSettings", Map.of(
            "number_of_shards", 1,
            "number_of_replicas", 0
        ));
        
        DataSyncEngine.SyncResult result = dataSyncEngine.executeSync(syncConfig);
        
        log.info("同步结果: {}", result.isSuccess() ? "成功" : "失败");
        log.info("对比结果数量: {}", result.getCompareResults() != null ? result.getCompareResults().size() : 0);
        log.info("迁移统计: {}", result.getMigrationStatistics());
        log.info("同步报告:\n{}", result.getReport());
    }
    
    /**
     * 复杂同步场景示例：多表关联数据同步
     */
    private void complexSyncExample() {
        log.info("--- 复杂同步场景示例 ---");
        
        // 用户表同步
        SyncConfig userSyncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("用户表同步")
                .syncType(SyncConfig.SyncType.SYNC)
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "****************************",
                    "root", "password", "app", "users"
                ))
                .targetConfig(SyncConfig.createMySQLConfig(
                    "****************************",
                    "root", "password", "app", "users"
                ))
                .batchSize(1000)
                .build();
        
        // 订单表同步
        SyncConfig orderSyncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("订单表同步")
                .syncType(SyncConfig.SyncType.SYNC)
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "****************************",
                    "root", "password", "app", "orders"
                ))
                .targetConfig(SyncConfig.createMySQLConfig(
                    "****************************",
                    "root", "password", "app", "orders"
                ))
                .batchSize(1000)
                .build();
        
        // 执行同步
        DataSyncEngine.SyncResult userResult = dataSyncEngine.executeSync(userSyncConfig);
        DataSyncEngine.SyncResult orderResult = dataSyncEngine.executeSync(orderSyncConfig);
        
        log.info("用户表同步结果: {}", userResult.isSuccess() ? "成功" : "失败");
        log.info("订单表同步结果: {}", orderResult.isSuccess() ? "成功" : "失败");
    }
}
