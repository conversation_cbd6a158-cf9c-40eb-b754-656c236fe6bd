package com.jiao.data.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 数据同步记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSyncRecord {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 源数据源ID
     */
    private Long sourceDataSourceId;

    /**
     * 目标数据源ID
     */
    private Long targetDataSourceId;

    /**
     * 源表名
     */
    private String sourceTableName;

    /**
     * 目标表名
     */
    private String targetTableName;

    /**
     * 同步类型：COMPARE、MIGRATE、SYNC
     */
    private String syncType;

    /**
     * 同步状态：PENDING、RUNNING、SUCCESS、FAILED
     */
    private String status;

    /**
     * 总记录数
     */
    private Long totalRecords;

    /**
     * 成功记录数
     */
    private Long successRecords;

    /**
     * 失败记录数
     */
    private Long failedRecords;

    /**
     * 跳过记录数
     */
    private Long skippedRecords;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 耗时（毫秒）
     */
    private Long duration;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 同步报告
     */
    private String report;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;
}
