package com.jiao.data.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据源实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSource {

    /**
     * 数据源ID
     */
    private Long id;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类型：MySQL、Redis、Elasticsearch
     */
    private String type;

    /**
     * 连接URL
     */
    private String url;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码（加密存储）
     */
    private String password;

    /**
     * 数据库名
     */
    private String database;

    /**
     * 连接状态：ACTIVE、INACTIVE、ERROR
     */
    private String status;

    /**
     * 额外配置参数
     */
    private Map<String, Object> properties;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
