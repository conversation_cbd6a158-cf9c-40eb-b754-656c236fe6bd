package com.jiao.data.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据对比记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataContrastRecord {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 同步任务ID
     */
    private String taskId;

    /**
     * 数据记录ID
     */
    private String recordId;

    /**
     * 对比结果类型：IDENTICAL、DIFFERENT、SOURCE_ONLY、TARGET_ONLY
     */
    private String resultType;

    /**
     * 源数据
     */
    private Map<String, Object> sourceData;

    /**
     * 目标数据
     */
    private Map<String, Object> targetData;

    /**
     * 差异详情
     */
    private Map<String, Object> differences;

    /**
     * 对比时间
     */
    private LocalDateTime compareTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
