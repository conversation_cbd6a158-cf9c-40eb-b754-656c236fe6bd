package com.jiao.data.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据同步配置类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncConfig {
    
    /**
     * 同步任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 源数据源配置
     */
    private DataSourceConfig sourceConfig;
    
    /**
     * 目标数据源配置
     */
    private DataSourceConfig targetConfig;
    
    /**
     * 同步类型：COMPARE（对比）、MIGRATE（迁移）、SYNC（同步）
     */
    private SyncType syncType;
    
    /**
     * 批处理大小
     */
    @Builder.Default
    private int batchSize = 1000;
    
    /**
     * 是否跳过已存在的记录
     */
    @Builder.Default
    private boolean skipExisting = false;
    
    /**
     * 遇到错误时是否继续
     */
    @Builder.Default
    private boolean continueOnError = true;
    
    /**
     * 字段映射配置
     */
    @Builder.Default
    private Map<String, String> fieldMapping = new HashMap<>();
    
    /**
     * 忽略的字段列表
     */
    @Builder.Default
    private List<String> ignoreFields = List.of();
    
    /**
     * 是否深度对比
     */
    @Builder.Default
    private boolean deepCompare = true;
    
    /**
     * 自定义配置参数
     */
    @Builder.Default
    private Map<String, Object> customConfig = new HashMap<>();
    
    /**
     * 数据源配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataSourceConfig {
        /**
         * 数据源类型：MySQL、Redis、Elasticsearch
         */
        private String type;
        
        /**
         * 连接URL
         */
        private String url;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 密码
         */
        private String password;
        
        /**
         * 数据库名
         */
        private String database;
        
        /**
         * 表名或集合名
         */
        private String tableName;
        
        /**
         * 额外的连接参数
         */
        @Builder.Default
        private Map<String, Object> properties = new HashMap<>();
    }
    
    /**
     * 同步类型枚举
     */
    public enum SyncType {
        COMPARE,    // 仅对比
        MIGRATE,    // 仅迁移
        SYNC        // 对比后迁移
    }
    
    /**
     * 获取合并后的配置参数
     */
    public Map<String, Object> getMergedConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("batchSize", batchSize);
        config.put("skipExisting", skipExisting);
        config.put("continueOnError", continueOnError);
        config.put("fieldMapping", fieldMapping);
        config.put("ignoreFields", ignoreFields);
        config.put("deepCompare", deepCompare);
        config.putAll(customConfig);
        return config;
    }
    
    /**
     * 验证配置的有效性
     */
    public boolean isValid() {
        if (taskId == null || taskId.trim().isEmpty()) {
            return false;
        }
        
        if (sourceConfig == null || targetConfig == null) {
            return false;
        }
        
        if (sourceConfig.getType() == null || sourceConfig.getTableName() == null) {
            return false;
        }
        
        if (targetConfig.getType() == null || targetConfig.getTableName() == null) {
            return false;
        }
        
        if (syncType == null) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 创建MySQL数据源配置
     */
    public static DataSourceConfig createMySQLConfig(String url, String username, String password, 
                                                   String database, String tableName) {
        return DataSourceConfig.builder()
                .type("MySQL")
                .url(url)
                .username(username)
                .password(password)
                .database(database)
                .tableName(tableName)
                .build();
    }
    
    /**
     * 创建Redis数据源配置
     */
    public static DataSourceConfig createRedisConfig(String url, String password, String tableName) {
        return DataSourceConfig.builder()
                .type("Redis")
                .url(url)
                .password(password)
                .tableName(tableName)
                .build();
    }
    
    /**
     * 创建Elasticsearch数据源配置
     */
    public static DataSourceConfig createElasticsearchConfig(String url, String username, String password, 
                                                           String indexName) {
        return DataSourceConfig.builder()
                .type("Elasticsearch")
                .url(url)
                .username(username)
                .password(password)
                .tableName(indexName)
                .build();
    }
}
