package com.jiao.data.controller;

import com.jiao.data.config.SyncConfig;
import com.jiao.data.sync.DataSyncEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 数据同步控制器
 * 提供REST API接口用于执行数据同步任务
 */
@Slf4j
@RestController
@RequestMapping("/api/data-sync")
public class DataSyncController {

    @Autowired
    private DataSyncEngine dataSyncEngine;

    /**
     * 执行数据同步任务
     */
    @PostMapping("/execute")
    public Map<String, Object> executeSync(@RequestBody SyncConfig syncConfig) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 设置任务ID（如果未提供）
            if (syncConfig.getTaskId() == null) {
                syncConfig.setTaskId(UUID.randomUUID().toString());
            }

            // 执行同步
            DataSyncEngine.SyncResult result = dataSyncEngine.executeSync(syncConfig);

            response.put("success", result.isSuccess());
            response.put("taskId", result.getTaskId());
            response.put("message", result.getMessage());
            response.put("duration", result.getDuration());
            response.put("report", result.getReport());

            if (result.getCompareResults() != null) {
                response.put("compareResultCount", result.getCompareResults().size());
            }

            if (result.getMigrationStatistics() != null) {
                response.put("migrationStatistics", result.getMigrationStatistics());
            }

        } catch (Exception e) {
            log.error("执行数据同步失败", e);
            response.put("success", false);
            response.put("message", "执行失败: " + e.getMessage());
        }

        return response;
    }

    /**
     * MySQL到MySQL的数据对比示例
     */
    @PostMapping("/compare/mysql-to-mysql")
    public Map<String, Object> compareMySQLToMySQL(@RequestBody Map<String, Object> request) {

        // 构建同步配置
        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("MySQL到MySQL数据对比")
                .syncType(SyncConfig.SyncType.COMPARE)
                .sourceConfig(SyncConfig.createMySQLConfig(
                        (String) request.get("sourceUrl"),
                        (String) request.get("sourceUsername"),
                        (String) request.get("sourcePassword"),
                        (String) request.get("sourceDatabase"),
                        (String) request.get("sourceTable")
                ))
                .targetConfig(SyncConfig.createMySQLConfig(
                        (String) request.get("targetUrl"),
                        (String) request.get("targetUsername"),
                        (String) request.get("targetPassword"),
                        (String) request.get("targetDatabase"),
                        (String) request.get("targetTable")
                ))
                .batchSize((Integer) request.getOrDefault("batchSize", 1000))
                .ignoreFields((List<String>) request.getOrDefault("ignoreFields", List.of()))
                .build();

        return executeSync(syncConfig);
    }


    /**
     * MySQL到MySQL的数据迁移示例
     */
    @PostMapping("/migrate/mysql-to-mysql")
    public Map<String, Object> migrateMySQLToMySQL(@RequestBody Map<String, Object> request) {

        // 构建同步配置
        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("MySQL到MySQL数据迁移")
                .syncType(SyncConfig.SyncType.MIGRATE)
                .sourceConfig(SyncConfig.createMySQLConfig(
                        (String) request.get("sourceUrl"),
                        (String) request.get("sourceUsername"),
                        (String) request.get("sourcePassword"),
                        (String) request.get("sourceDatabase"),
                        (String) request.get("sourceTable")
                ))
                .targetConfig(SyncConfig.createMySQLConfig(
                        (String) request.get("targetUrl"),
                        (String) request.get("targetUsername"),
                        (String) request.get("targetPassword"),
                        (String) request.get("targetDatabase"),
                        (String) request.get("targetTable")
                ))
                .batchSize((Integer) request.getOrDefault("batchSize", 1000))
                .ignoreFields((List<String>) request.getOrDefault("ignoreFields", List.of()))
                .build();

        return executeSync(syncConfig);
    }

    /**
     * MySQL到Redis的数据迁移示例
     */
    @PostMapping("/migrate/mysql-to-redis")
    public Map<String, Object> migrateMySQLToRedis(@RequestBody Map<String, Object> request) {

        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("MySQL到Redis数据迁移")
                .syncType(SyncConfig.SyncType.MIGRATE)
                .sourceConfig(SyncConfig.createMySQLConfig(
                        (String) request.get("sourceUrl"),
                        (String) request.get("sourceUsername"),
                        (String) request.get("sourcePassword"),
                        (String) request.get("sourceDatabase"),
                        (String) request.get("sourceTable")
                ))
                .targetConfig(SyncConfig.createRedisConfig(
                        (String) request.get("targetUrl"),
                        (String) request.get("targetPassword"),
                        (String) request.get("targetKeyPrefix")
                ))
                .batchSize((Integer) request.getOrDefault("batchSize", 1000))
                .skipExisting((Boolean) request.getOrDefault("skipExisting", false))
                .build();

        return executeSync(syncConfig);
    }

    /**
     * MySQL到Elasticsearch的数据同步示例
     */
    @PostMapping("/sync/mysql-to-elasticsearch")
    public Map<String, Object> syncMySQLToElasticsearch(@RequestBody Map<String, Object> request) {

        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("MySQL到Elasticsearch数据同步")
                .syncType(SyncConfig.SyncType.SYNC)
                .sourceConfig(SyncConfig.createMySQLConfig(
                        (String) request.get("sourceUrl"),
                        (String) request.get("sourceUsername"),
                        (String) request.get("sourcePassword"),
                        (String) request.get("sourceDatabase"),
                        (String) request.get("sourceTable")
                ))
                .targetConfig(SyncConfig.createElasticsearchConfig(
                        (String) request.get("targetUrl"),
                        (String) request.get("targetUsername"),
                        (String) request.get("targetPassword"),
                        (String) request.get("targetIndex")
                ))
                .batchSize((Integer) request.getOrDefault("batchSize", 1000))
                .fieldMapping((Map<String, String>) request.getOrDefault("fieldMapping", new HashMap<>()))
                .build();

        return executeSync(syncConfig);
    }

    /**
     * 获取支持的数据源类型
     */
    @GetMapping("/supported-datasources")
    public Map<String, Object> getSupportedDataSources() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("dataSources", List.of("MySQL", "Redis", "Elasticsearch"));
        response.put("syncTypes", List.of("COMPARE", "MIGRATE", "SYNC"));
        return response;
    }

    /**
     * 获取同步配置模板
     */
    @GetMapping("/config-template")
    public Map<String, Object> getConfigTemplate() {
        Map<String, Object> template = new HashMap<>();

        // MySQL配置模板
        Map<String, Object> mysqlTemplate = new HashMap<>();
        mysqlTemplate.put("type", "MySQL");
        mysqlTemplate.put("url", "************************************");
        mysqlTemplate.put("username", "username");
        mysqlTemplate.put("password", "password");
        mysqlTemplate.put("database", "database_name");
        mysqlTemplate.put("tableName", "table_name");

        // Redis配置模板
        Map<String, Object> redisTemplate = new HashMap<>();
        redisTemplate.put("type", "Redis");
        redisTemplate.put("url", "redis://localhost:6379");
        redisTemplate.put("password", "password");
        redisTemplate.put("tableName", "key_prefix");

        // Elasticsearch配置模板
        Map<String, Object> esTemplate = new HashMap<>();
        esTemplate.put("type", "Elasticsearch");
        esTemplate.put("url", "http://localhost:9200");
        esTemplate.put("username", "username");
        esTemplate.put("password", "password");
        esTemplate.put("tableName", "index_name");

        template.put("mysql", mysqlTemplate);
        template.put("redis", redisTemplate);
        template.put("elasticsearch", esTemplate);

        return template;
    }
}
