package com.jiao.data;

import com.jiao.data.config.SyncConfig;
import com.jiao.data.sync.DataSyncEngine;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据同步引擎测试类
 */
@SpringBootTest
@SpringJUnitConfig
public class DataSyncEngineTest {
    
    /**
     * 测试同步配置的创建和验证
     */
    @Test
    public void testSyncConfigCreation() {
        // 测试MySQL配置创建
        SyncConfig.DataSourceConfig mysqlConfig = SyncConfig.createMySQLConfig(
            "********************************",
            "root",
            "password",
            "test_db",
            "users"
        );
        
        assertEquals("MySQL", mysqlConfig.getType());
        assertEquals("********************************", mysqlConfig.getUrl());
        assertEquals("root", mysqlConfig.getUsername());
        assertEquals("password", mysqlConfig.getPassword());
        assertEquals("test_db", mysqlConfig.getDatabase());
        assertEquals("users", mysqlConfig.getTableName());
        
        // 测试Redis配置创建
        SyncConfig.DataSourceConfig redisConfig = SyncConfig.createRedisConfig(
            "redis://localhost:6379",
            "redis_password",
            "user_cache"
        );
        
        assertEquals("Redis", redisConfig.getType());
        assertEquals("redis://localhost:6379", redisConfig.getUrl());
        assertEquals("redis_password", redisConfig.getPassword());
        assertEquals("user_cache", redisConfig.getTableName());
        
        // 测试Elasticsearch配置创建
        SyncConfig.DataSourceConfig esConfig = SyncConfig.createElasticsearchConfig(
            "http://localhost:9200",
            "elastic",
            "elastic_password",
            "products"
        );
        
        assertEquals("Elasticsearch", esConfig.getType());
        assertEquals("http://localhost:9200", esConfig.getUrl());
        assertEquals("elastic", esConfig.getUsername());
        assertEquals("elastic_password", esConfig.getPassword());
        assertEquals("products", esConfig.getTableName());
    }
    
    /**
     * 测试完整同步配置的创建和验证
     */
    @Test
    public void testFullSyncConfigCreation() {
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("user_id", "id");
        fieldMapping.put("user_name", "name");
        
        SyncConfig syncConfig = SyncConfig.builder()
                .taskId(UUID.randomUUID().toString())
                .taskName("测试同步任务")
                .description("这是一个测试同步任务")
                .syncType(SyncConfig.SyncType.SYNC)
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "**********************************",
                    "root", "password", "source_db", "users"
                ))
                .targetConfig(SyncConfig.createRedisConfig(
                    "redis://localhost:6379", "password", "user"
                ))
                .batchSize(500)
                .skipExisting(true)
                .continueOnError(false)
                .fieldMapping(fieldMapping)
                .ignoreFields(List.of("create_time", "update_time"))
                .deepCompare(true)
                .build();
        
        // 验证配置
        assertTrue(syncConfig.isValid());
        assertEquals("测试同步任务", syncConfig.getTaskName());
        assertEquals(SyncConfig.SyncType.SYNC, syncConfig.getSyncType());
        assertEquals(500, syncConfig.getBatchSize());
        assertTrue(syncConfig.isSkipExisting());
        assertFalse(syncConfig.isContinueOnError());
        assertTrue(syncConfig.isDeepCompare());
        
        // 验证字段映射
        assertEquals("id", syncConfig.getFieldMapping().get("user_id"));
        assertEquals("name", syncConfig.getFieldMapping().get("user_name"));
        
        // 验证忽略字段
        assertTrue(syncConfig.getIgnoreFields().contains("create_time"));
        assertTrue(syncConfig.getIgnoreFields().contains("update_time"));
        
        // 验证合并配置
        Map<String, Object> mergedConfig = syncConfig.getMergedConfig();
        assertEquals(500, mergedConfig.get("batchSize"));
        assertEquals(true, mergedConfig.get("skipExisting"));
        assertEquals(false, mergedConfig.get("continueOnError"));
        assertEquals(true, mergedConfig.get("deepCompare"));
    }
    
    /**
     * 测试无效配置的验证
     */
    @Test
    public void testInvalidSyncConfig() {
        // 测试空任务ID
        SyncConfig invalidConfig1 = SyncConfig.builder()
                .taskId("")
                .taskName("测试任务")
                .syncType(SyncConfig.SyncType.COMPARE)
                .build();
        assertFalse(invalidConfig1.isValid());
        
        // 测试空源配置
        SyncConfig invalidConfig2 = SyncConfig.builder()
                .taskId("test-id")
                .taskName("测试任务")
                .syncType(SyncConfig.SyncType.COMPARE)
                .targetConfig(SyncConfig.createMySQLConfig(
                    "********************************",
                    "root", "password", "test", "users"
                ))
                .build();
        assertFalse(invalidConfig2.isValid());
        
        // 测试空同步类型
        SyncConfig invalidConfig3 = SyncConfig.builder()
                .taskId("test-id")
                .taskName("测试任务")
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "**********************************",
                    "root", "password", "source", "users"
                ))
                .targetConfig(SyncConfig.createMySQLConfig(
                    "**********************************",
                    "root", "password", "target", "users"
                ))
                .build();
        assertFalse(invalidConfig3.isValid());
    }
    
    /**
     * 测试同步类型枚举
     */
    @Test
    public void testSyncTypeEnum() {
        assertEquals(3, SyncConfig.SyncType.values().length);
        
        SyncConfig.SyncType compare = SyncConfig.SyncType.valueOf("COMPARE");
        assertEquals(SyncConfig.SyncType.COMPARE, compare);
        
        SyncConfig.SyncType migrate = SyncConfig.SyncType.valueOf("MIGRATE");
        assertEquals(SyncConfig.SyncType.MIGRATE, migrate);
        
        SyncConfig.SyncType sync = SyncConfig.SyncType.valueOf("SYNC");
        assertEquals(SyncConfig.SyncType.SYNC, sync);
    }
    
    /**
     * 测试自定义配置参数
     */
    @Test
    public void testCustomConfig() {
        SyncConfig syncConfig = SyncConfig.builder()
                .taskId("test-id")
                .taskName("测试任务")
                .syncType(SyncConfig.SyncType.MIGRATE)
                .sourceConfig(SyncConfig.createMySQLConfig(
                    "**********************************",
                    "root", "password", "source", "users"
                ))
                .targetConfig(SyncConfig.createRedisConfig(
                    "redis://localhost:6379", "password", "user"
                ))
                .build();
        
        // 添加自定义配置
        syncConfig.getCustomConfig().put("keyPrefix", "app:user:");
        syncConfig.getCustomConfig().put("timeout", 30000);
        syncConfig.getCustomConfig().put("retryCount", 3);
        
        Map<String, Object> mergedConfig = syncConfig.getMergedConfig();
        assertEquals("app:user:", mergedConfig.get("keyPrefix"));
        assertEquals(30000, mergedConfig.get("timeout"));
        assertEquals(3, mergedConfig.get("retryCount"));
    }
}
